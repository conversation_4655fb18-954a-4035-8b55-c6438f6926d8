#!/usr/bin/env python3
"""
测试异常分析和订单类型统计修复效果的完整脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backend'))

from database.duckdb_manager import DuckDBManager
from modules.contract_risk_analysis.services.user_analysis_trigger import UserAnalysisTrigger
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fix_effectiveness():
    """测试修复效果的主函数"""
    user_id = '9c8db566f6354095bf237e962d0ab4c5'
    
    print("=" * 80)
    print("🔧 异常分析和订单类型统计修复效果测试")
    print("=" * 80)
    
    db = DuckDBManager()
    
    # 第一步：检查异常交易原始数据
    print("\n📋 第一步：检查异常交易原始数据")
    print("-" * 50)
    
    risk_details = db.execute_sql('''
    SELECT 
        detection_type, 
        COUNT(*) as count, 
        SUM(abnormal_volume) as total_volume,
        MAX(created_at) as latest_time
    FROM contract_risk_details 
    WHERE member_id = ?
    GROUP BY detection_type
    ORDER BY latest_time DESC
    ''', [user_id])
    
    if risk_details:
        print("✅ 异常交易原始数据存在:")
        total_abnormal_volume = 0
        total_risk_events = 0
        for detail in risk_details:
            print(f"  📊 {detail['detection_type']}: {detail['count']}条记录, 总量{detail['total_volume']:.2f} USDT")
            print(f"      最新时间: {detail['latest_time']}")
            total_abnormal_volume += detail['total_volume']
            total_risk_events += detail['count']
        
        print(f"\n📈 汇总: 总异常交易量 {total_abnormal_volume:.2f} USDT, 总风险事件 {total_risk_events} 条")
    else:
        print("❌ 没有找到异常交易原始数据")
        return False
    
    # 第二步：检查持仓数据中的订单类型
    print("\n📋 第二步：检查持仓数据中的订单类型")
    print("-" * 50)
    
    order_stats = db.execute_sql('''
    SELECT 
        SUM(market_orders_open) as total_market_open,
        SUM(limit_orders_open) as total_limit_open,
        SUM(market_orders_close) as total_market_close,
        SUM(limit_orders_close) as total_limit_close,
        COUNT(*) as total_positions
    FROM position_analysis 
    WHERE member_id = ?
    ''', [user_id])
    
    if order_stats and order_stats[0]['total_positions'] > 0:
        stats = order_stats[0]
        print("✅ 持仓数据中的订单类型统计:")
        print(f"  📊 开仓市价单: {stats['total_market_open']}")
        print(f"  📊 开仓限价单: {stats['total_limit_open']}")
        print(f"  📊 平仓市价单: {stats['total_market_close']}")
        print(f"  📊 平仓限价单: {stats['total_limit_close']}")
        print(f"  📊 总持仓数: {stats['total_positions']}")
        
        total_orders = (stats['total_market_open'] + stats['total_limit_open'] + 
                       stats['total_market_close'] + stats['total_limit_close'])
        print(f"  📈 总订单数: {total_orders}")
        
        if total_orders == 0:
            print("❌ 订单类型统计为0，存在问题")
            return False
    else:
        print("❌ 没有找到持仓数据或订单类型统计")
        return False
    
    # 第三步：测试用户行为分析触发器
    print("\n📋 第三步：测试用户行为分析触发器")
    print("-" * 50)
    
    try:
        # 获取最新的task_id
        latest_task = db.execute_sql('''
        SELECT task_id FROM position_analysis 
        WHERE member_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
        ''', [user_id])
        
        if not latest_task:
            print("❌ 没有找到相关的task_id")
            return False
        
        task_id = latest_task[0]['task_id']
        print(f"✅ 找到task_id: {task_id[:8]}...")
        
        # 创建触发器并获取用户持仓数据
        trigger = UserAnalysisTrigger()
        user_positions = trigger._get_user_positions_from_task(task_id)
        
        if user_id not in user_positions:
            print(f"❌ 没有找到用户 {user_id} 的持仓数据")
            return False
        
        positions = user_positions[user_id]
        print(f"✅ 成功获取 {len(positions)} 条持仓数据")
        
        # 检查持仓数据是否包含订单类型字段
        if positions:
            sample_pos = positions[0]
            required_fields = ['market_orders_open', 'limit_orders_open', 'market_orders_close', 'limit_orders_close']
            
            missing_fields = []
            for field in required_fields:
                if field not in sample_pos:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 持仓数据缺少订单类型字段: {missing_fields}")
                return False
            else:
                print("✅ 持仓数据包含所有订单类型字段")
                print(f"  📊 样本数据: 开仓市价单={sample_pos['market_orders_open']}, 开仓限价单={sample_pos['limit_orders_open']}")
        
    except Exception as e:
        print(f"❌ 测试用户行为分析触发器失败: {e}")
        return False
    
    # 第四步：执行完整的用户行为分析
    print("\n📋 第四步：执行完整的用户行为分析")
    print("-" * 50)
    
    try:
        from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
        from modules.user_analysis.models.user_behavior_models import PositionData
        from datetime import datetime
        
        # 转换为PositionData对象
        position_objects = []
        for pos in positions:
            position_data = PositionData(
                position_id=pos['position_id'],
                member_id=user_id,
                contract_name=pos['contract_name'],
                primary_side=int(pos['primary_side']),
                open_time=pos['open_time'],
                close_time=pos['close_time'],
                duration_minutes=float(pos['duration_minutes'] or 0),
                total_open_amount=float(pos['total_open_amount'] or 0),
                total_close_amount=float(pos['total_close_amount'] or 0),
                avg_open_price=float(pos.get('avg_open_price', 0)),
                avg_close_price=float(pos.get('avg_close_price', 0)),
                total_pnl=float(pos['total_pnl'] or 0),
                total_commission=float(pos['total_commission'] or 0),
                net_pnl=float(pos['net_pnl'] or 0),
                leverage=float(pos['leverage'] or 1),
                # 关键：包含订单类型字段
                market_orders_open=int(pos.get('market_orders_open', 0)),
                limit_orders_open=int(pos.get('limit_orders_open', 0)),
                market_orders_close=int(pos.get('market_orders_close', 0)),
                limit_orders_close=int(pos.get('limit_orders_close', 0))
            )
            position_objects.append(position_data)
        
        print(f"✅ 成功转换 {len(position_objects)} 条PositionData对象")
        
        # 执行用户行为分析
        analyzer = UserBehaviorAnalyzer()
        analysis_result = analyzer.analyze_user_behavior(user_id, position_objects)
        
        print("✅ 用户行为分析执行成功")
        print(f"  📊 基础指标 - 总交易量: {analysis_result.basic_metrics.total_volume:.2f}")
        print(f"  📊 基础指标 - 总交易数: {analysis_result.basic_metrics.total_trades}")
        print(f"  📊 异常分析 - 异常交易量: {analysis_result.abnormal_analysis.abnormal_volume:.2f}")
        print(f"  📊 异常分析 - 对敲交易量: {analysis_result.abnormal_analysis.wash_trading_volume:.2f}")
        print(f"  📊 异常分析 - 风险事件数: {analysis_result.abnormal_analysis.risk_events_count}")
        
        # 保存结果
        from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer
        contract_analyzer = CTContractAnalyzer()
        contract_analyzer._save_user_behavior_result(user_id, analysis_result)
        
        print("✅ 用户行为分析结果保存成功")
        
    except Exception as e:
        print(f"❌ 执行用户行为分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 第五步：验证最终结果
    print("\n📋 第五步：验证最终结果")
    print("-" * 50)
    
    final_profile = db.execute_sql('''
    SELECT 
        abnormal_volume, abnormal_ratio, wash_trading_volume, 
        high_frequency_volume, risk_events_count,
        open_market_orders, open_limit_orders,
        close_market_orders, close_limit_orders,
        market_orders_ratio, limit_orders_ratio,
        total_volume, total_trades,
        updated_at
    FROM user_trading_profiles 
    WHERE member_id = ?
    ORDER BY updated_at DESC
    LIMIT 1
    ''', [user_id])
    
    if final_profile:
        p = final_profile[0]
        print("✅ 最终用户档案数据:")
        print(f"  📊 异常交易量: {p['abnormal_volume']:.2f} USDT")
        print(f"  📊 异常比例: {p['abnormal_ratio']:.4f} ({p['abnormal_ratio']*100:.2f}%)")
        print(f"  📊 对敲交易量: {p['wash_trading_volume']:.2f} USDT")
        print(f"  📊 高频交易量: {p['high_frequency_volume']:.2f} USDT")
        print(f"  📊 风险事件数: {p['risk_events_count']}")
        print(f"  📊 开仓市价单: {p['open_market_orders']}")
        print(f"  📊 开仓限价单: {p['open_limit_orders']}")
        print(f"  📊 平仓市价单: {p['close_market_orders']}")
        print(f"  📊 平仓限价单: {p['close_limit_orders']}")
        print(f"  📊 市价单比例: {p['market_orders_ratio']:.4f}")
        print(f"  📊 限价单比例: {p['limit_orders_ratio']:.4f}")
        print(f"  📊 总交易量: {p['total_volume']:.2f} USDT")
        print(f"  📊 总交易数: {p['total_trades']}")
        print(f"  📊 更新时间: {p['updated_at']}")
        
        # 验证修复效果
        success_checks = []
        
        # 检查异常分析
        if p['abnormal_volume'] > 0:
            success_checks.append("✅ 异常分析数据正确")
        else:
            success_checks.append("❌ 异常分析数据仍为0")
        
        # 检查订单类型统计
        total_orders = (p['open_market_orders'] + p['open_limit_orders'] + 
                       p['close_market_orders'] + p['close_limit_orders'])
        if total_orders > 0:
            success_checks.append("✅ 订单类型统计正确")
        else:
            success_checks.append("❌ 订单类型统计仍为0")
        
        # 检查数据一致性
        if abs(p['abnormal_volume'] - total_abnormal_volume) < 0.01:
            success_checks.append("✅ 异常数据与原始数据一致")
        else:
            success_checks.append(f"⚠️ 异常数据不一致: 档案{p['abnormal_volume']:.2f} vs 原始{total_abnormal_volume:.2f}")
        
        print(f"\n📈 修复效果验证:")
        for check in success_checks:
            print(f"  {check}")
        
        # 总体评估
        if all("✅" in check for check in success_checks):
            print(f"\n🎉 修复完全成功！所有问题都已解决")
            return True
        elif any("✅" in check for check in success_checks):
            print(f"\n⚠️ 修复部分成功，仍有问题需要解决")
            return False
        else:
            print(f"\n❌ 修复失败，问题仍然存在")
            return False
    else:
        print("❌ 没有找到最终的用户档案数据")
        return False

if __name__ == "__main__":
    print(f"开始时间: {datetime.now()}")
    
    try:
        success = test_fix_effectiveness()
        
        print("\n" + "=" * 80)
        if success:
            print("🎉 测试完成：修复效果验证成功！")
            print("✅ 异常分析和订单类型统计都已正确修复")
        else:
            print("❌ 测试完成：修复效果验证失败")
            print("⚠️ 仍有问题需要进一步解决")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"结束时间: {datetime.now()}")
