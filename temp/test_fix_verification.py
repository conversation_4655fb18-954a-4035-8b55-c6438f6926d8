#!/usr/bin/env python3
"""
验证异常分析修复是否有效
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backend'))

from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
from database.duckdb_manager import DuckDBManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_abnormal_analysis_fix():
    """测试异常分析修复"""
    user_id = '9c8db566f6354095bf237e962d0ab4c5'
    
    print(f"=== 测试用户 {user_id} 的异常分析修复 ===")
    
    # 1. 验证原始异常数据存在
    db = DuckDBManager()
    
    print("\n1. 检查原始异常交易数据...")
    risk_details = db.execute_sql('''
    SELECT 
        detection_type,
        COUNT(*) as count,
        SUM(abnormal_volume) as total_volume,
        AVG(risk_score) as avg_score
    FROM contract_risk_details 
    WHERE member_id = ?
    GROUP BY detection_type
    ''', [user_id])
    
    if risk_details:
        print("✅ 原始异常交易数据存在:")
        for detail in risk_details:
            print(f"  类型: {detail['detection_type']}")
            print(f"  数量: {detail['count']}")
            print(f"  总交易量: {detail['total_volume']:.2f}")
            print(f"  平均风险评分: {detail['avg_score']:.2f}")
    else:
        print("❌ 没有找到原始异常交易数据")
        return False
    
    # 2. 测试异常分析计算
    print("\n2. 测试异常分析计算...")
    
    try:
        from modules.user_analysis.models.user_behavior_models import BasicMetrics
        
        analyzer = UserBehaviorAnalyzer()
        basic_metrics = BasicMetrics()
        basic_metrics.total_volume = 10000000.0  # 设置总交易量
        
        # 调用异常分析计算
        abnormal_analysis = analyzer._calculate_abnormal_analysis(user_id, [], basic_metrics)
        
        print("✅ 异常分析计算成功:")
        print(f"  异常交易量: {abnormal_analysis.abnormal_volume:.2f}")
        print(f"  异常比例: {abnormal_analysis.abnormal_ratio:.4f}")
        print(f"  对敲交易量: {abnormal_analysis.wash_trading_volume:.2f}")
        print(f"  风险事件数: {abnormal_analysis.risk_events_count}")
        
        # 验证数据是否正确
        if abnormal_analysis.abnormal_volume > 0:
            print("🎉 修复成功！异常分析可以正确读取数据")
            return True
        else:
            print("❌ 修复失败！异常分析仍然无法读取数据")
            return False
            
    except Exception as e:
        print(f"❌ 异常分析计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_profile_before_after():
    """测试用户档案修复前后对比"""
    user_id = '9c8db566f6354095bf237e962d0ab4c5'
    
    print(f"\n=== 用户档案修复前后对比 ===")
    
    db = DuckDBManager()
    
    # 查看当前用户档案
    profile = db.execute_sql('''
    SELECT 
        abnormal_ratio, 
        abnormal_volume, 
        wash_trading_volume, 
        risk_events_count,
        analysis_date,
        updated_at
    FROM user_trading_profiles 
    WHERE member_id = ?
    ''', [user_id])
    
    if profile:
        p = profile[0]
        print("当前用户档案:")
        print(f"  异常比例: {p['abnormal_ratio']:.4f}")
        print(f"  异常交易量: {p['abnormal_volume']:.2f}")
        print(f"  对敲交易量: {p['wash_trading_volume']:.2f}")
        print(f"  风险事件数: {p['risk_events_count']}")
        print(f"  分析日期: {p['analysis_date']}")
        print(f"  更新时间: {p['updated_at']}")
        
        if p['abnormal_volume'] > 0:
            print("✅ 用户档案已包含异常交易数据")
            return True
        else:
            print("⚠️  用户档案尚未更新异常交易数据")
            print("💡 需要重新运行合约分析或用户行为分析来更新档案")
            return False
    else:
        print("❌ 未找到用户档案")
        return False

def main():
    """主函数"""
    print("🔧 异常分析修复验证测试")
    print("=" * 50)
    
    # 测试1: 验证异常分析计算修复
    test1_result = test_abnormal_analysis_fix()
    
    # 测试2: 验证用户档案状态
    test2_result = test_user_profile_before_after()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"  异常分析计算修复: {'✅ 成功' if test1_result else '❌ 失败'}")
    print(f"  用户档案数据状态: {'✅ 已更新' if test2_result else '⚠️  待更新'}")
    
    if test1_result:
        print("\n🎉 核心问题已修复！")
        print("💡 异常分析现在可以正确读取 contract_risk_details 表中的数据")
        
        if not test2_result:
            print("\n📝 下一步操作建议:")
            print("1. 重新运行合约分析，触发用户行为分析更新")
            print("2. 或者直接运行用户行为分析来更新用户档案")
            print("3. 这样用户档案就会显示正确的异常交易信息")
    else:
        print("\n❌ 修复未完全成功，需要进一步检查")
    
    return test1_result

if __name__ == "__main__":
    main()
