#!/usr/bin/env python3
"""
测试真实的合约分析流程中的订单类型统计修复
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backend'))

from modules.contract_risk_analysis.services.user_analysis_trigger import UserAnalysisTrigger
from database.duckdb_manager import DuckDBManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_analysis_flow():
    """测试真实的分析流程"""
    user_id = '9c8db566f6354095bf237e962d0ab4c5'
    
    print(f"=== 测试真实合约分析流程中的订单类型统计修复 ===")
    
    # 1. 获取该用户的task_id
    db = DuckDBManager()
    
    print("\n1. 查找用户相关的task_id...")
    task_sql = """
    SELECT DISTINCT task_id 
    FROM position_analysis 
    WHERE member_id = ? 
    AND task_id IS NOT NULL 
    AND task_id != ''
    ORDER BY task_id DESC
    LIMIT 1
    """
    
    task_results = db.execute_sql(task_sql, [user_id])
    if not task_results:
        print("❌ 没有找到相关的task_id")
        return False
    
    task_id = task_results[0]['task_id']
    print(f"✅ 找到task_id: {task_id}")
    
    # 2. 使用UserAnalysisTrigger获取用户持仓数据
    print("\n2. 使用UserAnalysisTrigger获取用户持仓数据...")
    
    trigger = UserAnalysisTrigger()
    
    # 测试_get_user_positions_from_task方法
    user_positions = trigger._get_user_positions_from_task(task_id)
    
    if user_id not in user_positions:
        print(f"❌ 没有找到用户 {user_id} 的持仓数据")
        return False
    
    positions = user_positions[user_id]
    print(f"✅ 找到 {len(positions)} 条持仓数据")
    
    # 3. 检查持仓数据是否包含订单类型字段
    print("\n3. 检查持仓数据是否包含订单类型字段...")
    
    if positions:
        sample_pos = positions[0]
        required_fields = ['market_orders_open', 'limit_orders_open', 'market_orders_close', 'limit_orders_close']
        
        missing_fields = []
        for field in required_fields:
            if field not in sample_pos:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少订单类型字段: {missing_fields}")
            return False
        else:
            print("✅ 所有订单类型字段都存在")
            
            # 显示样本数据
            print(f"样本数据:")
            print(f"  开仓市价单: {sample_pos['market_orders_open']}")
            print(f"  开仓限价单: {sample_pos['limit_orders_open']}")
            print(f"  平仓市价单: {sample_pos['market_orders_close']}")
            print(f"  平仓限价单: {sample_pos['limit_orders_close']}")
    
    # 4. 测试用户行为分析触发
    print("\n4. 测试用户行为分析触发...")
    
    try:
        # 直接调用用户行为分析
        from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
        from modules.user_analysis.models.user_behavior_models import PositionData
        from datetime import datetime

        # 转换为PositionData对象
        position_objects = []
        for pos in positions:
            position_data = PositionData(
                position_id=pos['position_id'],
                member_id=user_id,
                contract_name=pos['contract_name'],
                primary_side=int(pos['primary_side']),
                open_time=pos['open_time'],
                close_time=pos['close_time'],
                duration_minutes=float(pos['duration_minutes'] or 0),
                total_open_amount=float(pos['total_open_amount'] or 0),
                total_close_amount=float(pos['total_close_amount'] or 0),
                avg_open_price=float(pos.get('avg_open_price', 0)),
                avg_close_price=float(pos.get('avg_close_price', 0)),
                total_pnl=float(pos['total_pnl'] or 0),
                total_commission=float(pos['total_commission'] or 0),
                net_pnl=float(pos['net_pnl'] or 0),
                leverage=float(pos['leverage'] or 1),
                # 🔧 关键修复：添加订单类型字段
                market_orders_open=int(pos.get('market_orders_open', 0)),
                limit_orders_open=int(pos.get('limit_orders_open', 0)),
                market_orders_close=int(pos.get('market_orders_close', 0)),
                limit_orders_close=int(pos.get('limit_orders_close', 0))
            )
            position_objects.append(position_data)

        # 执行用户行为分析
        analyzer = UserBehaviorAnalyzer()
        analysis_result = analyzer.analyze_user_behavior(user_id, position_objects)

        # 保存结果
        from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer
        contract_analyzer = CTContractAnalyzer()
        contract_analyzer._save_user_behavior_result(user_id, analysis_result)

        success = True
        
        if success:
            print("✅ 用户行为分析触发成功")
            
            # 5. 验证保存结果
            print("\n5. 验证保存结果...")
            
            profile = db.execute_sql('''
            SELECT 
                open_market_orders, open_limit_orders,
                close_market_orders, close_limit_orders,
                market_orders_ratio, limit_orders_ratio,
                abnormal_volume, wash_trading_volume, risk_events_count,
                updated_at
            FROM user_trading_profiles 
            WHERE member_id = ?
            ORDER BY updated_at DESC
            LIMIT 1
            ''', [user_id])
            
            if profile:
                p = profile[0]
                print(f"最新用户档案数据:")
                print(f"  开仓市价单: {p['open_market_orders']}")
                print(f"  开仓限价单: {p['open_limit_orders']}")
                print(f"  平仓市价单: {p['close_market_orders']}")
                print(f"  平仓限价单: {p['close_limit_orders']}")
                print(f"  市价单比例: {p['market_orders_ratio']:.4f}")
                print(f"  限价单比例: {p['limit_orders_ratio']:.4f}")
                print(f"  异常交易量: {p['abnormal_volume']:.2f}")
                print(f"  对敲交易量: {p['wash_trading_volume']:.2f}")
                print(f"  风险事件数: {p['risk_events_count']}")
                print(f"  更新时间: {p['updated_at']}")
                
                total_orders = (p['open_market_orders'] + p['open_limit_orders'] + 
                               p['close_market_orders'] + p['close_limit_orders'])
                
                if total_orders > 0 and p['abnormal_volume'] > 0:
                    print(f"\n🎉 修复完全成功！")
                    print(f"  ✅ 订单类型统计正确: 总订单数 {total_orders}")
                    print(f"  ✅ 异常分析正确: 异常交易量 {p['abnormal_volume']:.2f}")
                    return True
                elif total_orders > 0:
                    print(f"\n✅ 订单类型统计修复成功，但异常分析可能还有问题")
                    return True
                else:
                    print(f"\n❌ 订单类型统计仍然为0")
                    return False
            else:
                print("❌ 未找到保存的用户档案")
                return False
        else:
            print("❌ 用户行为分析触发失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_analysis_flow()
    if success:
        print("\n✅ 真实分析流程测试成功")
    else:
        print("\n❌ 真实分析流程测试失败")
