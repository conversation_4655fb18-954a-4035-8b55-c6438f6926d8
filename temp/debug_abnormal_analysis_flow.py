#!/usr/bin/env python3
"""
调试异常分析完整流程
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backend'))

from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
from modules.user_analysis.models.user_behavior_models import PositionData
from database.duckdb_manager import DuckDBManager
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_abnormal_analysis_flow():
    """调试异常分析完整流程"""
    user_id = '9c8db566f6354095bf237e962d0ab4c5'
    
    print(f"=== 调试用户 {user_id} 的异常分析完整流程 ===")
    
    # 1. 检查异常交易原始数据
    print("\n1. 检查异常交易原始数据...")
    db = DuckDBManager()
    
    risk_details = db.execute_sql('''
    SELECT detection_type, COUNT(*) as count, SUM(abnormal_volume) as volume
    FROM contract_risk_details 
    WHERE member_id = ?
    GROUP BY detection_type
    ''', [user_id])
    
    if risk_details:
        print("✅ 异常交易原始数据存在:")
        for detail in risk_details:
            print(f"  {detail['detection_type']}: {detail['count']}条, 总量{detail['volume']:.2f}")
    else:
        print("❌ 没有找到异常交易原始数据")
        return False
    
    # 2. 获取真实的持仓数据
    print("\n2. 获取真实的持仓数据...")
    positions_data = db.execute_sql('''
    SELECT * FROM position_analysis 
    WHERE member_id = ? 
    ORDER BY open_time
    LIMIT 20
    ''', [user_id])
    
    print(f"找到 {len(positions_data)} 条持仓记录")
    
    if not positions_data:
        print("❌ 没有找到持仓数据")
        return False
    
    # 3. 转换为PositionData对象
    print("\n3. 转换持仓数据...")
    position_objects = []
    for pos in positions_data:
        try:
            position_data = PositionData(
                position_id=pos.get('position_id', ''),
                member_id=pos['member_id'],
                contract_name=pos.get('contract_name', ''),
                primary_side=1 if pos.get('primary_side', 'LONG') == 'LONG' else 3,
                open_time=datetime.fromisoformat(str(pos['open_time']).replace('Z', '+00:00')) if pos.get('open_time') else datetime.now(),
                close_time=datetime.fromisoformat(str(pos['close_time']).replace('Z', '+00:00')) if pos.get('close_time') else None,
                duration_minutes=float(pos.get('duration_minutes', 0)),
                total_open_amount=float(pos.get('total_open_amount', 0)),
                total_close_amount=float(pos.get('total_close_amount', 0)),
                avg_open_price=float(pos.get('avg_open_price', 0)),
                avg_close_price=float(pos.get('avg_close_price', 0)),
                total_pnl=float(pos.get('total_pnl', 0)),
                total_commission=float(pos.get('total_commission', 0)),
                net_pnl=float(pos.get('net_pnl', 0)),
                leverage=float(pos.get('avg_leverage', 1.0)),
                task_id=''
            )
            position_objects.append(position_data)
        except Exception as e:
            print(f"转换持仓数据失败: {e}")
            continue
    
    print(f"成功转换 {len(position_objects)} 条持仓数据")
    
    # 4. 执行用户行为分析
    print("\n4. 执行用户行为分析...")
    try:
        analyzer = UserBehaviorAnalyzer()
        
        # 4.1 先单独测试异常分析计算
        print("\n4.1 单独测试异常分析计算...")
        from modules.user_analysis.models.user_behavior_models import BasicMetrics
        basic_metrics = BasicMetrics()
        basic_metrics.total_volume = 36408444.0  # 使用真实的总交易量
        
        abnormal_analysis = analyzer._calculate_abnormal_analysis(user_id, position_objects, basic_metrics)
        print(f"单独异常分析结果:")
        print(f"  abnormal_volume: {abnormal_analysis.abnormal_volume}")
        print(f"  wash_trading_volume: {abnormal_analysis.wash_trading_volume}")
        print(f"  risk_events_count: {abnormal_analysis.risk_events_count}")
        
        # 4.2 执行完整的用户行为分析
        print("\n4.2 执行完整的用户行为分析...")
        analysis_result = analyzer.analyze_user_behavior(user_id, position_objects)
        
        print(f"完整分析结果:")
        print(f"  基础指标 - total_volume: {analysis_result.basic_metrics.total_volume}")
        print(f"  基础指标 - total_trades: {analysis_result.basic_metrics.total_trades}")
        print(f"  异常分析 - abnormal_volume: {analysis_result.abnormal_analysis.abnormal_volume}")
        print(f"  异常分析 - wash_trading_volume: {analysis_result.abnormal_analysis.wash_trading_volume}")
        print(f"  异常分析 - risk_events_count: {analysis_result.abnormal_analysis.risk_events_count}")
        
        # 4.3 检查是否有错误标识
        if hasattr(analysis_result, '_should_return_error') and analysis_result._should_return_error:
            print(f"⚠️  分析结果被标记为错误: {getattr(analysis_result, '_error_message', '未知原因')}")
            return False
        
        # 5. 模拟保存过程
        print("\n5. 模拟保存过程...")
        from modules.contract_risk_analysis.services.contract_analyzer import CTContractAnalyzer
        
        contract_analyzer = CTContractAnalyzer()
        
        # 保存前检查异常分析数据
        print(f"保存前异常分析数据:")
        print(f"  abnormal_volume: {analysis_result.abnormal_analysis.abnormal_volume}")
        print(f"  wash_trading_volume: {analysis_result.abnormal_analysis.wash_trading_volume}")
        print(f"  risk_events_count: {analysis_result.abnormal_analysis.risk_events_count}")
        
        # 执行保存
        contract_analyzer._save_user_behavior_result(user_id, analysis_result)
        print("✅ 保存完成")
        
        # 6. 验证保存结果
        print("\n6. 验证保存结果...")
        profile = db.execute_sql('''
        SELECT abnormal_volume, abnormal_ratio, wash_trading_volume, risk_events_count, updated_at
        FROM user_trading_profiles 
        WHERE member_id = ?
        ORDER BY updated_at DESC
        LIMIT 1
        ''', [user_id])
        
        if profile:
            p = profile[0]
            print(f"数据库中的数据:")
            print(f"  abnormal_volume: {p['abnormal_volume']}")
            print(f"  wash_trading_volume: {p['wash_trading_volume']}")
            print(f"  risk_events_count: {p['risk_events_count']}")
            print(f"  更新时间: {p['updated_at']}")
            
            if p['abnormal_volume'] > 0:
                print("\n🎉 调试成功！异常交易数据正确保存")
                return True
            else:
                print("\n❌ 调试失败！异常交易数据仍然为0")
                return False
        else:
            print("❌ 未找到保存的用户档案")
            return False
            
    except Exception as e:
        print(f"❌ 分析过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_abnormal_analysis_flow()
    if success:
        print("\n✅ 调试完成：异常分析流程正常")
    else:
        print("\n❌ 调试完成：异常分析流程存在问题")
