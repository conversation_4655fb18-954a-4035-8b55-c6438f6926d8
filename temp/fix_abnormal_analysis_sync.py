#!/usr/bin/env python3
"""
修复异常分析数据同步问题

问题描述：
- contract_risk_details 表中存在异常交易记录
- 但 user_trading_profiles 表中对应用户的异常指标为0
- 这是因为异常分析计算逻辑没有正确聚合数据

解决方案：
1. 重新计算所有用户的异常分析数据
2. 更新 user_trading_profiles 表
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backend'))

from database.duckdb_manager import DuckDBManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def calculate_user_abnormal_analysis(db_manager: DuckDBManager, member_id: str):
    """计算单个用户的异常分析数据"""
    try:
        # 查询用户的异常交易记录，按类型分组统计
        sql = """
        SELECT 
            detection_type,
            COUNT(*) as count,
            SUM(COALESCE(abnormal_volume, 0)) as total_volume,
            AVG(COALESCE(risk_score, 0)) as avg_risk_score
        FROM contract_risk_details 
        WHERE member_id = ?
        GROUP BY detection_type
        """
        
        results = db_manager.execute_sql(sql, [member_id])
        
        # 初始化数据
        wash_trading_volume = 0.0
        wash_trading_count = 0
        high_frequency_volume = 0.0
        high_frequency_count = 0
        funding_arbitrage_volume = 0.0
        funding_arbitrage_count = 0
        total_abnormal_volume = 0.0
        total_risk_events = 0
        
        # 聚合各类型的数据
        for result in results:
            detection_type = result['detection_type']
            count = int(result['count'])
            volume = float(result['total_volume']) if result['total_volume'] else 0.0

            total_abnormal_volume += volume
            total_risk_events += count

            if detection_type == 'wash_trading':
                wash_trading_volume += volume
                wash_trading_count += count
            elif detection_type == 'high_frequency':
                high_frequency_volume += volume
                high_frequency_count += count
            elif detection_type == 'funding_arbitrage':
                funding_arbitrage_volume += volume
                funding_arbitrage_count += count
        
        # 获取用户的总交易量来计算异常比例
        profile_sql = "SELECT total_volume FROM user_trading_profiles WHERE member_id = ?"
        profile_result = db_manager.execute_sql(profile_sql, [member_id])
        
        total_volume = 0.0
        if profile_result:
            total_volume = profile_result[0].get('total_volume', 0.0)
        
        # 计算异常比例
        abnormal_ratio = 0.0
        if total_volume > 0:
            abnormal_ratio = total_abnormal_volume / total_volume
        
        return {
            'abnormal_volume': total_abnormal_volume,
            'abnormal_ratio': abnormal_ratio,
            'wash_trading_volume': wash_trading_volume,
            'wash_trading_count': wash_trading_count,
            'high_frequency_volume': high_frequency_volume,
            'high_frequency_count': high_frequency_count,
            'funding_arbitrage_volume': funding_arbitrage_volume,
            'funding_arbitrage_count': funding_arbitrage_count,
            'risk_events_count': total_risk_events
        }
        
    except Exception as e:
        logger.error(f"计算用户 {member_id} 异常分析失败: {e}")
        return None

def update_user_abnormal_analysis(db_manager: DuckDBManager, member_id: str, analysis_data: dict):
    """更新用户的异常分析数据"""
    try:
        update_sql = """
        UPDATE user_trading_profiles 
        SET 
            abnormal_volume = ?,
            abnormal_ratio = ?,
            wash_trading_volume = ?,
            high_frequency_volume = ?,
            risk_events_count = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE member_id = ?
        """
        
        params = [
            analysis_data['abnormal_volume'],
            analysis_data['abnormal_ratio'],
            analysis_data['wash_trading_volume'],
            analysis_data['high_frequency_volume'],
            analysis_data['risk_events_count'],
            member_id
        ]
        
        db_manager.execute_sql(update_sql, params)
        return True
        
    except Exception as e:
        logger.error(f"更新用户 {member_id} 异常分析数据失败: {e}")
        return False

def fix_specific_user(member_id: str):
    """修复特定用户的异常分析数据"""
    db_manager = DuckDBManager()
    
    logger.info(f"开始修复用户 {member_id} 的异常分析数据...")
    
    # 计算异常分析数据
    analysis_data = calculate_user_abnormal_analysis(db_manager, member_id)
    
    if analysis_data is None:
        logger.error(f"计算用户 {member_id} 异常分析数据失败")
        return False
    
    logger.info(f"计算结果:")
    logger.info(f"  异常交易量: {analysis_data['abnormal_volume']:.2f}")
    logger.info(f"  异常比例: {analysis_data['abnormal_ratio']:.4f}")
    logger.info(f"  对敲交易量: {analysis_data['wash_trading_volume']:.2f}")
    logger.info(f"  高频交易量: {analysis_data['high_frequency_volume']:.2f}")
    logger.info(f"  风险事件数: {analysis_data['risk_events_count']}")
    
    # 更新数据库
    if update_user_abnormal_analysis(db_manager, member_id, analysis_data):
        logger.info(f"✅ 用户 {member_id} 异常分析数据修复成功")
        return True
    else:
        logger.error(f"❌ 用户 {member_id} 异常分析数据更新失败")
        return False

def fix_all_users_with_anomalies():
    """修复所有有异常记录但档案显示正常的用户"""
    db_manager = DuckDBManager()
    
    # 查找有异常记录但档案显示异常比例为0的用户
    sql = """
    SELECT DISTINCT crd.member_id
    FROM contract_risk_details crd
    LEFT JOIN user_trading_profiles utp ON crd.member_id = utp.member_id
    WHERE utp.abnormal_ratio = 0 OR utp.abnormal_ratio IS NULL
    """
    
    users_to_fix = db_manager.execute_sql(sql)
    
    logger.info(f"找到 {len(users_to_fix)} 个需要修复的用户")
    
    success_count = 0
    for user in users_to_fix:
        member_id = user['member_id']
        if fix_specific_user(member_id):
            success_count += 1
    
    logger.info(f"修复完成: {success_count}/{len(users_to_fix)} 个用户修复成功")

if __name__ == "__main__":
    # 修复特定用户
    target_user = "9c8db566f6354095bf237e962d0ab4c5"
    
    print(f"=== 修复用户 {target_user} 的异常分析数据 ===")
    
    if fix_specific_user(target_user):
        print("✅ 修复成功！")
        
        # 验证修复结果
        db_manager = DuckDBManager()
        profile = db_manager.execute_sql('SELECT * FROM user_trading_profiles WHERE member_id = ?', [target_user])
        if profile:
            p = profile[0]
            print(f"\n📊 修复后的数据:")
            print(f"  异常比例: {p.get('abnormal_ratio', 0):.4f}")
            print(f"  异常交易量: {p.get('abnormal_volume', 0):.2f}")
            print(f"  对敲交易量: {p.get('wash_trading_volume', 0):.2f}")
            print(f"  高频交易量: {p.get('high_frequency_volume', 0):.2f}")
            print(f"  风险事件数: {p.get('risk_events_count', 0)}")
    else:
        print("❌ 修复失败！")
